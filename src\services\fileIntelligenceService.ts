import { BaseService, ServiceError, ServiceErrorCode } from './base'
import {
  FileIntelligenceData,
  EntitySelection,
  SmartAnnotation,
  FileInteraction
} from '../types/intelligenceTypes'

import crypto from 'crypto'

/**
 * File Intelligence Service
 * Manages intelligence data storage and retrieval for file details overlay
 */
class FileIntelligenceService extends BaseService {
  private readonly INTELLIGENCE_FOLDER = '.intelligence'
  
  constructor() {
    super({
      name: 'FileIntelligenceService',
      autoInitialize: true
    })
  }

  protected async doInitialize(): Promise<void> {
    // Initialize file intelligence service
    this.logger.info('File Intelligence Service initialized')
  }

  /**
   * Generate document hash for consistent file identification
   */
  private generateDocumentHash(filePath: string): string {
    return crypto.createHash('sha256').update(filePath).digest('hex').substring(0, 16)
  }

  /**
   * Get intelligence file path for a document
   */
  private getIntelligenceFilePath(vaultPath: string, documentHash: string): string {
    return `${vaultPath}/${this.INTELLIGENCE_FOLDER}/${documentHash}/intelligence.json`
  }

  /**
   * Ensure intelligence directory exists
   */
  private async ensureIntelligenceDirectory(vaultPath: string, documentHash: string): Promise<void> {
    const intelligenceDir = `${vaultPath}/${this.INTELLIGENCE_FOLDER}/${documentHash}`
    
    if (window.electronAPI?.vault?.createDirectory) {
      const result = await window.electronAPI.vault.createDirectory(intelligenceDir)
      if (!result.success) {
        throw new ServiceError(
          ServiceErrorCode.STORAGE_ERROR,
          `Failed to create intelligence directory: ${result.error}`,
          { serviceName: this.serviceName, operation: 'ensureIntelligenceDirectory' }
        )
      }
    }
  }

  /**
   * Save file intelligence data
   */
  async saveFileIntelligence(vaultPath: string, data: FileIntelligenceData): Promise<boolean> {
    const result = await this.executeOperation(
      'saveFileIntelligence',
      async () => {
        await this.ensureIntelligenceDirectory(vaultPath, data.document_hash)
        
        const filePath = this.getIntelligenceFilePath(vaultPath, data.document_hash)
        
        if (window.electronAPI?.vault?.writeFile) {
          const writeResult = await window.electronAPI.vault.writeFile(filePath, JSON.stringify(data, null, 2))
          if (!writeResult.success) {
            throw new ServiceError(
              ServiceErrorCode.STORAGE_ERROR,
              `Failed to save intelligence data: ${writeResult.error}`,
              { serviceName: this.serviceName, operation: 'saveFileIntelligence' }
            )
          }
        }
        
        return true
      }
    )

    if (!result.success) {
      this.logger.error('Failed to save file intelligence', 'saveFileIntelligence', result.error)
      // toastService.error('Save Failed', 'Could not save intelligence data')
      return false
    }

    return result.data!
  }

  /**
   * Load file intelligence data
   */
  async loadFileIntelligence(vaultPath: string, filePath: string): Promise<FileIntelligenceData | null> {
    const result = await this.executeOperation(
      'loadFileIntelligence',
      async () => {
        const documentHash = this.generateDocumentHash(filePath)
        const intelligenceFilePath = this.getIntelligenceFilePath(vaultPath, documentHash)
        
        if (window.electronAPI?.vault?.readFile) {
          const readResult = await window.electronAPI.vault.readFile(intelligenceFilePath)
          if (!readResult.success) {
            // File doesn't exist yet, return null
            return null
          }
          
          try {
            const data = JSON.parse(readResult.content!) as FileIntelligenceData
            return data
          } catch (error) {
            this.logger.warn('Invalid intelligence data format', 'loadFileIntelligence', error)
            return null
          }
        }
        
        return null
      }
    )

    if (!result.success) {
      this.logger.error('Failed to load file intelligence', 'loadFileIntelligence', result.error)
      return null
    }

    return result.data!
  }

  /**
   * Create initial file intelligence data structure
   */
  createInitialIntelligenceData(filePath: string, fileName: string, vaultName: string, contextId?: string): FileIntelligenceData {
    const documentHash = this.generateDocumentHash(filePath)
    
    return {
      document_hash: documentHash,
      file_path: filePath,
      file_name: fileName,
      file_type: fileName.split('.').pop()?.toLowerCase() || 'unknown',
      vault_name: vaultName,
      context_id: contextId,
      last_updated: new Date().toISOString(),
      entity_selections: [],
      smart_annotations: [],
      user_notes: [],
      ai_analysis: null,
      interaction_history: [],
      annotation_navigation: {
        current_note_index: 0,
        total_notes: 0,
        note_order: [],
        last_viewed_note: null,
        last_navigation_timestamp: new Date().toISOString(),
        created_timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Generate mock entities for demonstration (will be replaced with AI analysis)
   */
  generateMockEntities(fileName: string): EntitySelection[] {
    const fileExtension = fileName.split('.').pop()?.toLowerCase() || ''
    
    const mockEntities: Partial<EntitySelection>[] = []
    
    // Generate entities based on file type
    if (fileExtension === 'pdf') {
      mockEntities.push(
        { entity_text: 'UI Design', entity_type: 'content_category', confidence: 0.95 },
        { entity_text: 'Specifications', entity_type: 'technical_concept', confidence: 0.88 },
        { entity_text: 'Requirements', entity_type: 'requirement', confidence: 0.82 },
        { entity_text: 'Components', entity_type: 'feature', confidence: 0.75 },
        { entity_text: 'Design System', entity_type: 'methodology', confidence: 0.70 },
        { entity_text: 'User Interface', entity_type: 'technical_concept', confidence: 0.65 }
      )
    } else if (fileExtension === 'md') {
      mockEntities.push(
        { entity_text: 'Documentation', entity_type: 'content_category', confidence: 0.92 },
        { entity_text: 'API Reference', entity_type: 'technical_concept', confidence: 0.85 },
        { entity_text: 'Code Examples', entity_type: 'feature', confidence: 0.78 },
        { entity_text: 'Installation', entity_type: 'action_item', confidence: 0.72 },
        { entity_text: 'Configuration', entity_type: 'requirement', confidence: 0.68 }
      )
    } else {
      mockEntities.push(
        { entity_text: 'File Content', entity_type: 'content_category', confidence: 0.80 },
        { entity_text: 'Data Structure', entity_type: 'technical_concept', confidence: 0.75 },
        { entity_text: 'Information', entity_type: 'other', confidence: 0.70 }
      )
    }

    // Convert to full EntitySelection objects
    return mockEntities.map((entity, index) => ({
      entity_id: `entity_${Date.now()}_${index}`,
      entity_text: entity.entity_text!,
      entity_type: entity.entity_type!,
      confidence: entity.confidence!,
      is_selected: index < 3, // Pre-select top 3
      selection_timestamp: new Date().toISOString(),
      color_category: index === 0 ? 'primary' : index === 1 ? 'secondary' : index === 2 ? 'tertiary' : 'default',
      rank: index + 1,
      context_snippet: `Context for ${entity.entity_text}`
    }))
  }

  /**
   * Update entity selections
   */
  async updateEntitySelections(vaultPath: string, filePath: string, selections: EntitySelection[]): Promise<boolean> {
    const data = await this.loadFileIntelligence(vaultPath, filePath)
    if (!data) return false

    data.entity_selections = selections
    data.last_updated = new Date().toISOString()
    
    // Add interaction record
    const interaction: FileInteraction = {
      interaction_id: `int_${Date.now()}`,
      action_type: 'entity_select',
      timestamp: new Date().toISOString(),
      data: { selections: selections.map(s => ({ entity_id: s.entity_id, is_selected: s.is_selected })) },
      session_id: `session_${Date.now()}`
    }
    data.interaction_history.push(interaction)

    return await this.saveFileIntelligence(vaultPath, data)
  }

  /**
   * Add smart annotation
   */
  async addSmartAnnotation(vaultPath: string, filePath: string, annotation: SmartAnnotation): Promise<boolean> {
    const data = await this.loadFileIntelligence(vaultPath, filePath)
    if (!data) return false

    data.smart_annotations.push(annotation)
    data.last_updated = new Date().toISOString()
    
    // Add interaction record
    const interaction: FileInteraction = {
      interaction_id: `int_${Date.now()}`,
      action_type: 'annotation_create',
      timestamp: new Date().toISOString(),
      data: { annotation_id: annotation.annotation_id },
      session_id: `session_${Date.now()}`
    }
    data.interaction_history.push(interaction)

    return await this.saveFileIntelligence(vaultPath, data)
  }

  /**
   * Update smart annotation
   */
  async updateSmartAnnotation(vaultPath: string, filePath: string, annotationId: string, content: string): Promise<boolean> {
    const data = await this.loadFileIntelligence(vaultPath, filePath)
    if (!data) return false

    const annotation = data.smart_annotations.find(a => a.annotation_id === annotationId)
    if (!annotation) return false

    annotation.content = content
    annotation.last_edited = new Date().toISOString()
    data.last_updated = new Date().toISOString()
    
    // Add interaction record
    const interaction: FileInteraction = {
      interaction_id: `int_${Date.now()}`,
      action_type: 'annotation_edit',
      timestamp: new Date().toISOString(),
      data: { annotation_id: annotationId, new_content: content },
      session_id: `session_${Date.now()}`
    }
    data.interaction_history.push(interaction)

    return await this.saveFileIntelligence(vaultPath, data)
  }
}

export const fileIntelligenceService = new FileIntelligenceService()
