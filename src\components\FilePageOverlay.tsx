import React, { useState, useEffect, useRef } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICONS } from './Icons/index';
import { pdfViewerService, PDFViewerState } from '../services/PDFViewerService';
import { fileViewerService, FileViewerState } from '../services/FileViewerService';
import * as pdfjsLib from 'pdfjs-dist';

// Set up PDF.js worker - use local copy in public directory
pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js';

// Helper function to properly decode base64 to UTF-8
const decodeBase64ToUTF8 = (base64String: string): string => {
  try {
    const binaryString = atob(base64String);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    const decoder = new TextDecoder('utf-8');
    return decoder.decode(bytes);
  } catch (error) {
    console.error('Error decoding base64 to UTF-8:', error);
    return base64String; // Return original if decoding fails
  }
};

interface FilePageOverlayProps {
  onClose: () => void;
}

interface FileTypeInfo {
  type: 'pdf' | 'markdown' | 'mermaid' | 'text' | 'image' | 'code' | 'unsupported';
  extension: string;
  mimeType?: string;
  canExtractText: boolean;
  canAnnotate: boolean;
  requiresProcessing: boolean;
  extractionMethod: string;
  displayName: string;
}

export const FilePageOverlay: React.FC<FilePageOverlayProps> = ({ onClose }) => {
  const [state, setState] = useState<FileViewerState>(fileViewerService.getState());
  const [pdfState, setPdfState] = useState<PDFViewerState>(pdfViewerService.getState());
  const [pdfDocument, setPdfDocument] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [fileTypeInfo, setFileTypeInfo] = useState<FileTypeInfo | null>(null);
  const [fileContent, setFileContent] = useState<string>('');
  const [editedContent, setEditedContent] = useState<string>('');
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  const [isPdfReady, setIsPdfReady] = useState<boolean>(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Helper function to check if file supports editing
  const supportsEdit = (fileName?: string): boolean => {
    if (!fileName) return false;
    const ext = fileName.split('.').pop()?.toLowerCase();
    const editableExtensions = [
      'md', 'markdown', 'txt', 'log', 'csv', 'xml', 'json', 'yaml', 'yml',
      'js', 'ts', 'tsx', 'jsx', 'html', 'css', 'scss', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt'
    ];
    return ext ? editableExtensions.includes(ext) : false;
  };

  // File type detection function
  const detectFileType = (fileName: string): FileTypeInfo => {
    const extension = fileName.split('.').pop()?.toLowerCase() || '';

    // PDF files
    if (extension === 'pdf') {
      return {
        type: 'pdf',
        extension,
        mimeType: 'application/pdf',
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: true,
        extractionMethod: 'pdf-parse',
        displayName: 'PDF Document'
      };
    }

    // Markdown files
    if (['md', 'markdown'].includes(extension)) {
      return {
        type: 'markdown',
        extension,
        mimeType: 'text/markdown',
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: false,
        extractionMethod: 'direct-read',
        displayName: 'Markdown Document'
      };
    }

    // Image files
    if (['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp', 'bmp', 'ico'].includes(extension)) {
      return {
        type: 'image',
        extension,
        mimeType: `image/${extension === 'jpg' ? 'jpeg' : extension}`,
        canExtractText: false,
        canAnnotate: false,
        requiresProcessing: false,
        extractionMethod: 'none',
        displayName: 'Image File'
      };
    }

    // Text files
    if (['txt', 'log', 'csv', 'xml', 'json', 'yaml', 'yml'].includes(extension)) {
      return {
        type: 'text',
        extension,
        mimeType: 'text/plain',
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: false,
        extractionMethod: 'direct-read',
        displayName: 'Text File'
      };
    }

    // Code files
    if (['js', 'ts', 'tsx', 'jsx', 'html', 'css', 'scss', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt'].includes(extension)) {
      return {
        type: 'code',
        extension,
        mimeType: 'text/plain',
        canExtractText: true,
        canAnnotate: true,
        requiresProcessing: false,
        extractionMethod: 'direct-read',
        displayName: 'Code File'
      };
    }

    // Unsupported
    return {
      type: 'unsupported',
      extension,
      mimeType: 'application/octet-stream',
      canExtractText: false,
      canAnnotate: false,
      requiresProcessing: false,
      extractionMethod: 'none',
      displayName: 'Unsupported File'
    };
  };

  // Detect if content is a Mermaid diagram
  const isMermaidContent = (content: string): boolean => {
    const trimmedContent = content.trim();

    // Check for common Mermaid diagram types
    const mermaidPatterns = [
      /^graph\s+(TD|TB|BT|RL|LR)/i,           // Flowchart
      /^flowchart\s+(TD|TB|BT|RL|LR)/i,       // Flowchart (new syntax)
      /^sequenceDiagram/i,                     // Sequence diagram
      /^classDiagram/i,                        // Class diagram
      /^stateDiagram/i,                        // State diagram
      /^erDiagram/i,                           // Entity relationship diagram
      /^gantt/i,                               // Gantt chart
      /^pie\s+title/i,                         // Pie chart
      /^journey/i,                             // User journey
      /^gitgraph/i,                            // Git graph
      /^mindmap/i,                             // Mind map
      /^timeline/i,                            // Timeline
      /^quadrantChart/i,                       // Quadrant chart
      /^requirement/i,                         // Requirement diagram
      /^C4Context/i,                           // C4 diagram
    ];

    // Check if content starts with any Mermaid pattern
    return mermaidPatterns.some(pattern => pattern.test(trimmedContent));
  };

  // Load generic file content (non-PDF files)
  const loadGenericFileContent = async (typeInfo: FileTypeInfo): Promise<void> => {
    if (!state.filePath) return;

    try {
      fileViewerService.setLoading(true);
      
      if (!window.electronAPI?.files?.getFileContent) {
        throw new Error('getFileContent API not available');
      }

      const result = await window.electronAPI.files.getFileContent(state.filePath);
      console.log('File getFileContent result:', result ? 'success' : 'failed', result?.length || 0);

      if (!result) {
        throw new Error('Empty or invalid file content received');
      }

      // Convert Buffer to string if needed
      const resultString = typeof result === 'string' ? result : result.toString('base64');
      
      if (resultString.trim() === '') {
        throw new Error('Empty file content after conversion');
      }

      if (typeInfo.type === 'image') {
        // For images, keep as base64
        setFileContent(resultString);
      } else {
        // For text-based files, decode from base64 with proper UTF-8 handling
        const textContent = decodeBase64ToUTF8(resultString);
        setFileContent(textContent);

        // Check if markdown content is actually a Mermaid diagram
        if (typeInfo.type === 'markdown' && isMermaidContent(textContent)) {
          const updatedTypeInfo = { ...typeInfo, type: 'mermaid' as const, displayName: 'Mermaid Diagram' };
          setFileTypeInfo(updatedTypeInfo);
        }
      }
    } catch (error) {
      console.error('Failed to load generic file content:', error);
      throw error;
    } finally {
      fileViewerService.setLoading(false);
    }
  };

  // Load PDF with PDF.js with proper preload logic
  const loadPDFWithPDFJS = async (): Promise<void> => {
    if (!state.filePath) return;

    try {
      fileViewerService.setLoading(true);
      setIsPdfReady(false);
      console.log('Loading PDF with PDF.js:', state.filePath);

      if (!window.electronAPI?.files?.getFileContent) {
        throw new Error('getFileContent API not available');
      }

      // Get file content as base64 for PDF.js
      const result = await window.electronAPI.files.getFileContent(state.filePath);
      console.log('PDF getFileContent result:', result ? 'success' : 'failed', result?.length || 0);
      
      if (!result) {
        throw new Error('Empty or invalid file content received');
      }

      // Convert Buffer to string if needed
      const resultString = typeof result === 'string' ? result : result.toString('base64');
      
      if (resultString.trim() === '') {
        throw new Error('Empty file content after conversion');
      }

      // Convert base64 to Uint8Array for PDF.js
      const binaryString = atob(resultString);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      // Load PDF document with PDF.js
      console.log('Loading PDF document with PDF.js...');
      const loadingTask = pdfjsLib.getDocument({ data: bytes });
      const pdf = await loadingTask.promise;
      
      console.log('PDF.js loaded successfully:', pdf.numPages, 'pages');
      setPdfDocument(pdf);
      setTotalPages(pdf.numPages);
      setCurrentPage(1);

      // Preload first page to ensure it's ready for rendering
      console.log('Preloading first page...');
      const firstPage = await pdf.getPage(1);
      await firstPage.getOperatorList(); // This ensures the page is fully loaded
      
      console.log('First page preloaded successfully');
      setIsPdfReady(true);

      // Small delay to ensure React state updates and canvas is ready
      setTimeout(async () => {
        console.log('Rendering first page after preload...');
        await renderPage(pdf, 1);
        fileViewerService.setLoading(false);
      }, 100);

    } catch (error) {
      console.error('Failed to load PDF with PDF.js:', error);
      setIsPdfReady(false);
      fileViewerService.setLoading(false);
      throw error;
    }
  };

  // Main file content loader
  const loadFileContent = async (typeInfo: FileTypeInfo): Promise<void> => {
    if (!state.filePath) return;

    console.log('Loading file content for:', state.filePath, 'Type:', typeInfo.type);

    try {
      if (typeInfo.type === 'pdf') {
        await loadPDFWithPDFJS();
      } else {
        await loadGenericFileContent(typeInfo);
      }
    } catch (error) {
      console.error('Failed to load file content:', error);
      // Error is already handled in the specific loaders
    }
  };

  // Render PDF page with proper error handling
  const renderPage = async (pdf: any, pageNumber: number): Promise<void> => {
    if (!pdf || !canvasRef.current) {
      console.log('Cannot render: missing pdf or canvas ref');
      return;
    }

    try {
      console.log(`Starting to render page ${pageNumber}`);
      const page = await pdf.getPage(pageNumber);
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');
      
      if (!context) {
        throw new Error('Cannot get canvas context');
      }

      // Calculate viewport and scale
      const viewport = page.getViewport({ scale: pdfState.zoom / 100 });
      canvas.height = viewport.height;
      canvas.width = viewport.width;

      // Render the page
      const renderContext = {
        canvasContext: context,
        viewport: viewport
      };

      console.log(`Rendering page ${pageNumber} at ${pdfState.zoom}% zoom`);
      await page.render(renderContext).promise;
      console.log(`Page ${pageNumber} rendered successfully`);

    } catch (error) {
      console.error(`Failed to render page ${pageNumber}:`, error);
      throw error;
    }
  };

  // Event handlers
  const handleClose = () => {
    fileViewerService.closeFile();
    onClose();
  };

  const handleZoomIn = () => {
    const newZoom = Math.min(pdfState.zoom + 25, 400);
    pdfViewerService.setZoom(newZoom);
    if (pdfDocument && currentPage) {
      renderPage(pdfDocument, currentPage);
    }
  };

  const handleZoomOut = () => {
    const newZoom = Math.max(pdfState.zoom - 25, 25);
    pdfViewerService.setZoom(newZoom);
    if (pdfDocument && currentPage) {
      renderPage(pdfDocument, currentPage);
    }
  };

  const resetZoom = () => {
    pdfViewerService.setZoom(100);
    if (pdfDocument && currentPage) {
      renderPage(pdfDocument, currentPage);
    }
  };

  const toggleEditMode = () => {
    if (supportsEdit(state.fileName || undefined)) {
      setIsEditMode(!isEditMode);
      if (!isEditMode) {
        setEditedContent(fileContent);
      }
    }
  };

  const saveFile = async () => {
    if (!state.filePath || !editedContent) return;

    try {
      // Use the vault API to write file content
      await window.electronAPI?.invoke('vault:writeFile', state.filePath, editedContent);
      setFileContent(editedContent);
      setIsEditMode(false);
      console.log('File saved successfully');
    } catch (error) {
      console.error('Failed to save file:', error);
    }
  };

  const getFileIcon = () => {
    if (!fileTypeInfo) return ICONS.file;
    
    switch (fileTypeInfo.type) {
      case 'pdf': return ICONS.filePdf;
      case 'markdown': return ICONS.fileLines;
      case 'image': return ICONS.fileImage;
      case 'code': return ICONS.fileCode;
      default: return ICONS.file;
    }
  };

  // Mermaid renderer component
  const MermaidRenderer: React.FC<{ content: string }> = ({ content }) => {
    const mermaidRef = useRef<HTMLDivElement>(null);

    const renderMermaid = async () => {
      if (!mermaidRef.current) return;

      try {
        // Import mermaid with proper error handling
        const mermaidModule = await import('mermaid');
        const mermaid = mermaidModule.default || mermaidModule;
        
        mermaid.initialize({
          startOnLoad: false,
          theme: 'dark',
          securityLevel: 'loose'
        });

        const { svg } = await mermaid.render('mermaid-diagram', content);
        mermaidRef.current.innerHTML = svg;
      } catch (error) {
        console.error('Failed to render Mermaid diagram:', error);
        mermaidRef.current.innerHTML = `<pre class="text-red-400">Failed to render diagram: ${error}</pre>`;
      }
    };

    useEffect(() => {
      renderMermaid();
    }, [content]);

    return (
      <div className="bg-white rounded-lg p-4 overflow-auto">
        <div ref={mermaidRef} className="mermaid-diagram"></div>
      </div>
    );
  };

  // File content renderer
  const renderFileContent = () => {
    if (!fileTypeInfo) return null;

    switch (fileTypeInfo.type) {
      case 'pdf':
        return renderPDFContent();
      case 'markdown':
        return renderMarkdownContent();
      case 'mermaid':
        return renderMermaidContent();
      case 'text':
      case 'code':
        return renderTextContent();
      case 'image':
        return renderImageContent();
      default:
        return renderUnsupportedContent();
    }
  };

  const renderPDFContent = () => (
    <div className="flex flex-col items-center justify-center h-full">
      {!isPdfReady ? (
        <div className="text-center text-gray-400">
          <FontAwesomeIcon icon={ICONS.spinner} className="text-4xl mb-4 animate-spin" />
          <p className="text-lg font-medium">Preparing PDF...</p>
          <p className="text-sm">Loading and processing document</p>
        </div>
      ) : (
        <div className="relative w-full h-full flex flex-col">
          <canvas
            ref={canvasRef}
            className="mx-auto border border-gray-600 rounded-lg shadow-lg"
            style={{ maxWidth: '100%', maxHeight: 'calc(100vh - 200px)' }}
          />
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-4 mt-4 p-2 bg-gray-800 rounded-lg">
              <button
                onClick={() => handlePrevPage()}
                disabled={currentPage <= 1}
                className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600 transition-colors"
              >
                <FontAwesomeIcon icon={ICONS.chevronLeft} className="text-sm" />
              </button>
              <span className="text-sm text-gray-300">
                Page {currentPage} of {totalPages}
              </span>
              <button
                onClick={() => handleNextPage()}
                disabled={currentPage >= totalPages}
                className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600 transition-colors"
              >
                <FontAwesomeIcon icon={ICONS.chevronRight} className="text-sm" />
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );

  const renderMarkdownContent = () => {
    const htmlContent = renderMarkdownToHTML(fileContent);
    return (
      <div className="prose prose-invert max-w-none p-6">
        <div dangerouslySetInnerHTML={{ __html: htmlContent }} />
      </div>
    );
  };

  const renderMermaidContent = () => (
    <div className="p-6">
      <MermaidRenderer content={fileContent} />
    </div>
  );

  const renderTextContent = () => {
    if (isEditMode) {
      return (
        <div className="p-6">
          <textarea
            value={editedContent}
            onChange={(e) => setEditedContent(e.target.value)}
            className="w-full h-full bg-gray-800 text-gray-200 p-4 rounded-lg border border-gray-600 focus:border-primary/50 focus:outline-none resize-none font-mono text-sm"
          />
        </div>
      );
    }

    return (
      <div className="p-6">
        <pre className="text-gray-200 font-mono text-sm whitespace-pre-wrap overflow-auto h-full">
          {fileContent}
        </pre>
      </div>
    );
  };

  const renderImageContent = () => (
    <div className="flex items-center justify-center h-full p-6">
      <img
        src={`data:image/${fileTypeInfo?.extension};base64,${fileContent}`}
        alt="File preview"
        className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
      />
    </div>
  );

  const renderUnsupportedContent = () => (
    <div className="flex items-center justify-center h-full">
      <div className="text-center text-gray-400">
        <FontAwesomeIcon icon={ICONS.file} className="text-6xl mb-4" />
        <h3 className="text-xl font-semibold mb-2">Unsupported File Type</h3>
        <p className="text-sm">Cannot preview .{fileTypeInfo?.extension} files</p>
      </div>
    </div>
  );

  // Simple markdown to HTML renderer
  const renderMarkdownToHTML = (markdown: string): string => {
    let html = markdown;

    // Headers
    html = html.replace(/^### (.*$)/gim, '<h3 class="text-lg font-semibold text-white mt-6 mb-3">$1</h3>');
    html = html.replace(/^## (.*$)/gim, '<h2 class="text-xl font-semibold text-white mt-8 mb-4">$1</h2>');
    html = html.replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold text-white mt-8 mb-6">$1</h1>');

    // Bold and italic
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-white">$1</strong>');
    html = html.replace(/\*(.*?)\*/g, '<em class="italic text-gray-200">$1</em>');

    // Code blocks
    html = html.replace(/```([\s\S]*?)```/g, '<pre class="bg-gray-700 p-4 rounded-lg my-4 overflow-x-auto"><code class="text-sm font-mono text-gray-100">$1</code></pre>');

    // Inline code
    html = html.replace(/`(.*?)`/g, '<code class="bg-gray-700 px-2 py-1 rounded text-sm font-mono text-gray-100">$1</code>');

    // Links
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-primary hover:text-primary/80 underline" target="_blank" rel="noopener noreferrer">$1</a>');

    // Lists
    html = html.replace(/^\* (.*$)/gim, '<li class="text-gray-200 mb-1">$1</li>');
    html = html.replace(/^- (.*$)/gim, '<li class="text-gray-200 mb-1">$1</li>');

    // Wrap lists
    html = html.replace(/(<li.*<\/li>)/gs, '<ul class="list-disc list-inside space-y-1 my-4 text-gray-200">$1</ul>');

    // Paragraphs
    html = html.replace(/\n\n/g, '</p><p class="text-gray-200 mb-4">');
    html = '<p class="text-gray-200 mb-4">' + html + '</p>';

    // Clean up empty paragraphs
    html = html.replace(/<p class="text-gray-200 mb-4"><\/p>/g, '');

    return html;
  };

  const handleNextPage = async () => {
    if (pdfDocument && currentPage < totalPages) {
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      await renderPage(pdfDocument, nextPage);
    }
  };

  const handlePrevPage = async () => {
    if (pdfDocument && currentPage > 1) {
      const prevPage = currentPage - 1;
      setCurrentPage(prevPage);
      await renderPage(pdfDocument, prevPage);
    }
  };

  // Effects
  useEffect(() => {
    const unsubscribe = fileViewerService.subscribe(setState);
    return unsubscribe;
  }, []);

  useEffect(() => {
    const unsubscribe = pdfViewerService.subscribe(setPdfState);
    return unsubscribe;
  }, []);

  useEffect(() => {
    if (state.isOpen && state.filePath && state.fileName) {
      const typeInfo = detectFileType(state.fileName);
      setFileTypeInfo(typeInfo);
      loadFileContent(typeInfo);
    }
  }, [state.filePath, state.isOpen, state.fileName]);

  if (!state.isOpen) return null;

  return (
    <div id="pdf-viewer-overlay" className="absolute inset-0 bg-gray-900 z-50 flex">
      {/* Center Column - PDF Viewer */}
      <div id="pdf-viewer-panel" className="flex-1 bg-gray-800 flex flex-col border-r border-tertiary/50">
        {/* PDF Viewer Header */}
        <div className="p-4 border-b border-tertiary/50 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <button onClick={handleClose} className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
              <FontAwesomeIcon icon={ICONS.arrowLeft} className="text-gray-400 text-sm" />
            </button>
            <FontAwesomeIcon icon={getFileIcon()} className="text-secondary text-lg" />
            <span className="text-supplement1 font-semibold text-lg">{state.fileName || 'project-spec.pdf'}</span>
          </div>
          <div className="flex items-center gap-2">
            <button onClick={handleZoomOut} className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
              <FontAwesomeIcon icon={ICONS.searchMinus} className="text-gray-400 text-sm" />
            </button>
            <span className="text-gray-400 text-sm">{Math.round(fileTypeInfo?.type === 'pdf' ? pdfState.zoom : state.zoom)}%</span>
            <button onClick={handleZoomIn} className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
              <FontAwesomeIcon icon={ICONS.searchPlus} className="text-gray-400 text-sm" />
            </button>
            <button className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
              <FontAwesomeIcon icon={ICONS.download} className="text-gray-400 text-sm" />
            </button>
          </div>
        </div>
        
        {/* PDF Content */}
        <div id="pdf-content" className="flex-1 overflow-y-auto bg-gray-900 p-4">
          {state.isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-gray-400">
                <FontAwesomeIcon icon={ICONS.spinner} className="text-4xl mb-4 animate-spin" />
                <p className="text-lg font-medium">Loading {fileTypeInfo?.displayName || 'File'}...</p>
                <p className="text-sm">Please wait while we load the document</p>
              </div>
            </div>
          ) : (
            renderFileContent()
          )}
        </div>
      </div>

      {/* Right Column - File Details */}
      <div id="file-details-panel" className="w-1/3 bg-gray-800 flex flex-col overflow-hidden min-w-[320px] max-w-[480px]" style={{resize: "horizontal"}}>
        {/* File Header */}
        <div className="p-4 border-b border-tertiary/50 flex items-center justify-between">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <FontAwesomeIcon icon={getFileIcon()} className="text-secondary text-lg flex-shrink-0" />
            <span className="text-supplement1 font-semibold text-sm truncate">{state.fileName || 'project-spec.pdf'}</span>
          </div>
          <div className="flex items-center gap-1 ml-2">
            <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
              <FontAwesomeIcon icon={ICONS.lock} className="text-red-500 text-sm" />
              <div className="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                Private Document
              </div>
            </button>
            <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
              <FontAwesomeIcon icon={ICONS.ellipsisVertical} className="text-gray-400 text-sm" />
              <div className="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                System Operations
              </div>
            </button>
            <button onClick={handleClose} className="p-1 hover:bg-gray-700 rounded transition-colors">
              <FontAwesomeIcon icon={ICONS.xmark} className="text-gray-400 text-sm" />
            </button>
          </div>
        </div>
        
        {/* Tags Section */}
        <div className="p-3 border-b border-tertiary/50">
          <p className="text-xs text-gray-400 mb-2">Select any key ideas about this doc to enhance the AI context learning.</p>
          <div className="flex flex-wrap gap-1 mb-2">
            <span className="px-2 py-0.5 bg-primary/20 text-primary text-xs rounded-full border border-primary/30 font-medium">UI Design</span>
            <span className="px-2 py-0.5 bg-secondary/20 text-secondary text-xs rounded-full border border-secondary/30 font-medium">Specifications</span>
            <span className="px-2 py-0.5 bg-supplement2/20 text-supplement2 text-xs rounded-full border border-supplement2/30 font-medium">Requirements</span>
            <span className="px-2 py-0.5 bg-gray-700 text-gray-300 text-xs rounded-full">Components</span>
            <span className="px-2 py-0.5 bg-gray-700 text-gray-300 text-xs rounded-full">Wireframes</span>
            <span className="px-2 py-0.5 bg-gray-700 text-gray-300 text-xs rounded-full">Architecture</span>
            <span className="px-2 py-0.5 bg-gray-700 text-gray-300 text-xs rounded-full">Testing</span>
            <button className="px-2 py-0.5 border border-dashed border-gray-600 text-gray-400 text-xs rounded-full hover:border-gray-500 transition-colors">
              <FontAwesomeIcon icon={ICONS.plus} className="mr-1" />Add
            </button>
          </div>
        </div>
        
        {/* Document Summary */}
        <div className="p-3 border-b border-tertiary/50 flex-1 overflow-y-auto">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <h3 className="text-supplement1 font-semibold text-sm">Note #3</h3>
              <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                <FontAwesomeIcon icon={ICONS.trash} className="text-red-500 text-xs" />
                <div className="absolute bottom-6 left-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                  Delete Note
                </div>
              </button>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                  <FontAwesomeIcon icon={ICONS.penToSquare} className="text-supplement2 text-xs" />
                  <div className="absolute bottom-6 left-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                    Edit Tags
                  </div>
                </button>
                <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                  <FontAwesomeIcon icon={ICONS.floppyDisk} className="text-primary text-xs" />
                  <div className="absolute bottom-6 left-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                    Save Changes
                  </div>
                </button>
              </div>
              <div className="flex items-center gap-1 text-xs text-gray-400">
                <span className="">3 of 12</span>
                <button className="p-0.5 hover:bg-gray-700 rounded transition-colors">
                  <FontAwesomeIcon icon={ICONS.chevronLeft} className="text-xs" />
                </button>
                <button className="p-0.5 hover:bg-gray-700 rounded transition-colors">
                  <FontAwesomeIcon icon={ICONS.chevronRight} className="text-xs" />
                </button>
              </div>
            </div>
          </div>
          <div className="bg-gray-900 rounded-lg p-3 border border-tertiary/50 mb-3">
            <p className="text-gray-300 text-xs leading-relaxed mb-2">Document Summary:<br/><br/>
                This comprehensive project specification document outlines the complete design system requirements for the Chatlo application. It includes detailed UI components, color schemes, typography guidelines, and interaction patterns.
            </p>
            <p className="text-gray-300 text-xs leading-relaxed mb-2">
                The document covers 47 pages of technical specifications including:
            </p>
            <ul className="text-gray-300 text-xs space-y-1 mb-2 pl-3">
                <li className="flex items-start gap-1">
                    <span className="text-primary mt-0.5 text-xs">•</span>
                    <span className="">Component library specifications (buttons, forms, navigation)</span>
                </li>
                <li className="flex items-start gap-1">
                    <span className="text-secondary mt-0.5 text-xs">•</span>
                    <span className="">Color palette and theming guidelines</span>
                </li>
                <li className="flex items-start gap-1">
                    <span className="text-supplement2 mt-0.5 text-xs">•</span>
                    <span className="">Responsive design breakpoints and layouts</span>
                </li>
                <li className="flex items-start gap-1">
                    <span className="text-primary mt-0.5 text-xs">•</span>
                    <span className="">Accessibility requirements and testing protocols</span>
                </li>
            </ul>
            <p className="text-gray-400 text-xs">
                Last updated: January 15, 2024 • 2.1 MB • 47 pages
            </p>
          </div>
          
          {/* Example Prompts */}
          <div className="mb-3">
            <h4 className="text-supplement1 font-medium text-xs mb-2">Example Annotation Prompts</h4>
            <div className="space-y-1">
              <button className="w-full text-left p-1.5 bg-gray-900/50 hover:bg-gray-900 rounded text-xs text-gray-300 border border-tertiary/30 hover:border-tertiary/50 transition-colors leading-tight">
                  "Summarize the second paragraph"
              </button>
              <button className="w-full text-left p-1.5 bg-gray-900/50 hover:bg-gray-900 rounded text-xs text-gray-300 border border-tertiary/30 hover:border-tertiary/50 transition-colors leading-tight">
                  "Extract all color codes and create a palette reference"
              </button>
              <button className="w-full text-left p-1.5 bg-gray-900/50 hover:bg-gray-900 rounded text-xs text-gray-300 border border-tertiary/30 hover:border-tertiary/50 transition-colors leading-tight">
                  "List all component specifications with their properties"
              </button>
              <button className="w-full text-left p-1.5 bg-gray-900/50 hover:bg-gray-900 rounded text-xs text-gray-300 border border-tertiary/30 hover:border-tertiary/50 transition-colors leading-tight">
                  "Identify potential implementation challenges and solutions"
              </button>
              <button className="w-full text-left p-1.5 bg-gray-900/50 hover:bg-gray-900 rounded text-xs text-gray-300 border border-tertiary/30 hover:border-tertiary/50 transition-colors leading-tight">
                  "Compare this with existing design systems and best practices"
              </button>
              <button className="w-full text-left p-1.5 bg-gray-900/50 hover:bg-gray-900 rounded text-xs text-gray-300 border border-tertiary/30 hover:border-tertiary/50 transition-colors leading-tight">
                  "Generate test cases for accessibility compliance"
              </button>
            </div>
          </div>
          
          {/* Custom Prompt Input */}
          <div className="mb-3">
            <textarea placeholder="Enter your custom annotation prompt here..." className="w-full bg-gray-900 border border-tertiary/50 rounded-lg p-2 text-xs text-gray-300 placeholder-gray-500 resize-none focus:outline-none focus:border-primary/50 transition-colors" rows={3}></textarea>
          </div>
        </div>
        
        {/* Smart Annotation Button Set */}
        <div className="p-3 border-t border-tertiary/50">
          {/* Smart Annotation Header */}
          <div className="mb-3 p-3 bg-gradient-to-r from-primary/10 to-supplement2/10 border border-primary/20 rounded-lg cursor-pointer hover:border-primary/30 transition-colors">
            <div className="flex items-center gap-2 mb-1">
              <FontAwesomeIcon icon={ICONS.wandMagicSparkles} className="text-primary text-sm" />
              <h3 className="text-primary font-semibold text-sm">Smart Annotation</h3>
            </div>
            <p className="text-gray-300 text-xs leading-relaxed">
              One-click detail summarization and write your thoughts for context building.
            </p>
          </div>
          
          {/* Action Buttons */}
          <div className="flex gap-2">
            {/* Ask AI Button */}
            <button className="flex-1 flex items-center justify-center gap-2 p-3 bg-secondary/20 border border-secondary/30 rounded-lg hover:bg-secondary/30 transition-colors group">
              <FontAwesomeIcon icon={ICONS.comments} className="text-secondary text-sm" />
              <span className="text-secondary font-medium text-sm">Ask AI</span>
              <div className="absolute bottom-12 left-1/2 transform -translate-x-1/2 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                Send to chat to explore more with other LLM models
              </div>
            </button>
            
            {/* Extract Text Button */}
            <button className="flex-1 flex items-center justify-center gap-2 p-3 bg-supplement2/20 border border-supplement2/30 rounded-lg hover:bg-supplement2/30 transition-colors group">
              <FontAwesomeIcon icon={ICONS.arrowsRotate} className="text-supplement2 text-sm" />
              <span className="text-supplement2 font-medium text-sm">Extract Text</span>
              <div className="absolute bottom-12 left-1/2 transform -translate-x-1/2 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                OCR the text and copy to the clipboard
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
